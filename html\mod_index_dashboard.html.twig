{% extends "layout_default.html.twig" %}

{% block meta_title %}{{ 'Dashboard'|trans }}{% endblock %}

{% block breadcrumb %}
    <li class="breadcrumb-item active">{{ 'Dashboard'|trans }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white shadow-soft-lg hover-lift">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="h3 mb-2 fw-bold">{{ 'Welcome back'|trans }}, {{ profile.first_name }}!</h1>
                            <p class="mb-0 opacity-75">{{ 'Here\'s what\'s happening with your account today.'|trans }}</p>
                        </div>
                        <div class="col-md-4 text-end d-none d-md-block">
                            <i class="fas fa-chart-line fa-3x opacity-25"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3 col-sm-6">
            <div class="card hover-lift shadow-soft animate-fade-in-up">
                <div class="card-body text-center p-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-shopping-cart text-primary fs-4"></i>
                    </div>
                    <h3 class="h4 fw-bold text-primary mb-1">{{ orders_count|default(0) }}</h3>
                    <p class="text-muted mb-0 fw-medium">{{ 'Active Orders'|trans }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card hover-lift shadow-soft animate-fade-in-up">
                <div class="card-body text-center p-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-server text-success fs-4"></i>
                    </div>
                    <h3 class="h4 fw-bold text-success mb-1">{{ services_count|default(0) }}</h3>
                    <p class="text-muted mb-0 fw-medium">{{ 'Active Services'|trans }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card hover-lift shadow-soft animate-fade-in-up">
                <div class="card-body text-center p-4">
                    <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-file-invoice-dollar text-warning fs-4"></i>
                    </div>
                    <h3 class="h4 fw-bold text-warning mb-1">{{ invoices_count|default(0) }}</h3>
                    <p class="text-muted mb-0 fw-medium">{{ 'Pending Invoices'|trans }}</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 col-sm-6">
            <div class="card hover-lift shadow-soft animate-fade-in-up">
                <div class="card-body text-center p-4">
                    <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                        <i class="fas fa-headset text-info fs-4"></i>
                    </div>
                    <h3 class="h4 fw-bold text-info mb-1">{{ tickets_count|default(0) }}</h3>
                    <p class="text-muted mb-0 fw-medium">{{ 'Open Tickets'|trans }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row g-4 mb-4">
        <div class="col-md-8">
            <div class="card shadow-soft hover-lift">
                <div class="card-header bg-gradient-secondary text-white">
                    <h5 class="card-title mb-0 fw-semibold">
                        <i class="fas fa-bolt me-2"></i>{{ 'Quick Actions'|trans }}
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <a href="{{ 'order'|link }}" class="btn btn-outline-primary w-100 hover-lift">
                                <i class="fas fa-plus-circle me-2"></i>{{ 'New Order'|trans }}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ 'support'|link }}" class="btn btn-outline-success w-100 hover-lift">
                                <i class="fas fa-life-ring me-2"></i>{{ 'Get Support'|trans }}
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ 'client/profile'|link }}" class="btn btn-outline-info w-100 hover-lift">
                                <i class="fas fa-user-edit me-2"></i>{{ 'Edit Profile'|trans }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow-soft hover-lift">
                <div class="card-header bg-gradient-success text-white">
                    <h5 class="card-title mb-0 fw-semibold">
                        <i class="fas fa-wallet me-2"></i>{{ 'Account Balance'|trans }}
                    </h5>
                </div>
                <div class="card-body text-center p-4">
                    <h2 class="h1 fw-bold text-success mb-2">{{ client.client_balance_get_total|money(profile.currency) }}</h2>
                    <p class="text-muted mb-3">{{ 'Available Credit'|trans }}</p>
                    <a href="{{ 'client/balance'|link }}" class="btn btn-success btn-sm hover-lift">
                        <i class="fas fa-plus me-1"></i>{{ 'Add Funds'|trans }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row g-4">
        <div class="col-md-6">
            <div class="card shadow-soft hover-lift">
                <div class="card-header">
                    <h5 class="card-title mb-0 fw-semibold">
                        <i class="fas fa-clock me-2 text-primary"></i>{{ 'Recent Orders'|trans }}
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_orders|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for order in recent_orders %}
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1 fw-semibold">{{ order.title }}</h6>
                                            <small class="text-muted">{{ order.created_at|date('M d, Y') }}</small>
                                        </div>
                                        <span class="badge bg-{{ order.status == 'active' ? 'success' : 'warning' }} rounded-pill">
                                            {{ order.status|title }}
                                        </span>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{{ 'No recent orders'|trans }}</p>
                            <a href="{{ 'order'|link }}" class="btn btn-primary btn-sm">{{ 'Place Your First Order'|trans }}</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-soft hover-lift">
                <div class="card-header">
                    <h5 class="card-title mb-0 fw-semibold">
                        <i class="fas fa-bell me-2 text-warning"></i>{{ 'Notifications'|trans }}
                    </h5>
                </div>
                <div class="card-body">
                    {% if notifications|length > 0 %}
                        <div class="list-group list-group-flush">
                            {% for notification in notifications %}
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex align-items-start">
                                        <div class="bg-{{ notification.type|default('info') }} bg-opacity-10 rounded-circle p-2 me-3">
                                            <i class="fas fa-{{ notification.icon|default('info-circle') }} text-{{ notification.type|default('info') }}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1 fw-semibold">{{ notification.title }}</h6>
                                            <p class="mb-1 text-muted small">{{ notification.message }}</p>
                                            <small class="text-muted">{{ notification.created_at|date('M d, Y H:i') }}</small>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <p class="text-muted">{{ 'No new notifications'|trans }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
