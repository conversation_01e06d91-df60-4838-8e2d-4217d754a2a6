/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[1].use[2]!./node_modules/resolve-url-loader/index.js??ruleSet[1].rules[4].oneOf[1].use[3]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[1].use[4]!./assets/scss/markdown.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
@import url(https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap);
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[1].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[1].use[2]!./node_modules/resolve-url-loader/index.js??ruleSet[1].rules[4].oneOf[1].use[3]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[1].use[4]!./assets/scss/markdown.scss (1) ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
.markdown-body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 1rem;
  line-height: 1.7;
  color: #374151;
  max-width: none;
}
.markdown-body h1, .markdown-body h2, .markdown-body h3, .markdown-body h4, .markdown-body h5, .markdown-body h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #111827;
}
.markdown-body h1:first-child, .markdown-body h2:first-child, .markdown-body h3:first-child, .markdown-body h4:first-child, .markdown-body h5:first-child, .markdown-body h6:first-child {
  margin-top: 0;
}
.markdown-body h1 {
  font-size: 2.25rem;
  border-bottom: 3px solid #6366f1;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
}
.markdown-body h2 {
  font-size: 1.875rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}
.markdown-body h3 {
  font-size: 1.5rem;
  color: #6366f1;
}
.markdown-body h4 {
  font-size: 1.25rem;
}
.markdown-body h5 {
  font-size: 1.125rem;
}
.markdown-body h6 {
  font-size: 1rem;
  color: #6b7280;
}
.markdown-body p {
  margin-bottom: 1.25rem;
}
.markdown-body p:last-child {
  margin-bottom: 0;
}
.markdown-body a {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}
.markdown-body a:hover {
  color: rgb(52.2, 56.1, 236.8);
  text-decoration: underline;
  text-decoration-color: #6366f1;
  text-decoration-thickness: 2px;
  text-underline-offset: 3px;
}
.markdown-body ul, .markdown-body ol {
  margin-bottom: 1.25rem;
  padding-left: 1.5rem;
}
.markdown-body ul li, .markdown-body ol li {
  margin-bottom: 0.5rem;
}
.markdown-body ul li::marker, .markdown-body ol li::marker {
  color: #6366f1;
}
.markdown-body ul ul, .markdown-body ul ol, .markdown-body ol ul, .markdown-body ol ol {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.markdown-body code {
  background: #f3f4f6;
  color: #dc2626;
  padding: 0.125rem 0.375rem;
  border-radius: 0.5rem;
  font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
  font-size: 0.875rem;
  font-weight: 500;
}
.markdown-body pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.markdown-body pre code {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-size: 0.875rem;
}
.markdown-body blockquote {
  border-left: 4px solid #6366f1;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 0.75rem 0.75rem 0;
  font-style: italic;
}
.markdown-body blockquote p {
  margin-bottom: 0;
  font-size: 1.125rem;
  color: #4b5563;
}
.markdown-body blockquote::before {
  content: '"';
  font-size: 3rem;
  color: #6366f1;
  opacity: 0.3;
  line-height: 1;
  float: left;
  margin-right: 0.5rem;
  margin-top: -0.5rem;
}
.markdown-body table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.markdown-body table thead {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}
.markdown-body table thead th {
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border: none;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}
.markdown-body table tbody tr {
  transition: background-color 0.3s ease;
}
.markdown-body table tbody tr:hover {
  background: rgba(99, 102, 241, 0.02);
}
.markdown-body table tbody tr:nth-child(even) {
  background: #f9fafb;
}
.markdown-body table tbody td {
  padding: 1rem;
  border: none;
  border-bottom: 1px solid #e5e7eb;
  vertical-align: top;
}
.markdown-body table tbody td:last-child {
  border-bottom: none;
}
.markdown-body img {
  max-width: 100%;
  height: auto;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  margin: 1rem 0;
}
.markdown-body img.img-fluid {
  border-radius: 0.75rem;
}
.markdown-body hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #6366f1, transparent);
  margin: 2rem 0;
}
.markdown-body .task-list-item {
  list-style: none;
  margin-left: -1.5rem;
}
.markdown-body .task-list-item input[type=checkbox] {
  margin-right: 0.5rem;
  transform: scale(1.2);
  accent-color: #6366f1;
}

[data-bs-theme=dark] .markdown-body {
  color: #e5e7eb;
}
[data-bs-theme=dark] .markdown-body h1, [data-bs-theme=dark] .markdown-body h2, [data-bs-theme=dark] .markdown-body h3, [data-bs-theme=dark] .markdown-body h4, [data-bs-theme=dark] .markdown-body h5, [data-bs-theme=dark] .markdown-body h6 {
  color: #f9fafb;
}
[data-bs-theme=dark] .markdown-body h6 {
  color: #9ca3af;
}
[data-bs-theme=dark] .markdown-body code {
  background: #374151;
  color: #fbbf24;
}
[data-bs-theme=dark] .markdown-body blockquote {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
}
[data-bs-theme=dark] .markdown-body blockquote p {
  color: #d1d5db;
}
[data-bs-theme=dark] .markdown-body table thead {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}
[data-bs-theme=dark] .markdown-body table thead th {
  color: #e5e7eb;
}
[data-bs-theme=dark] .markdown-body table tbody tr:nth-child(even) {
  background: #1f2937;
}
[data-bs-theme=dark] .markdown-body table tbody tr:hover {
  background: rgba(99, 102, 241, 0.1);
}
[data-bs-theme=dark] .markdown-body table tbody td {
  border-bottom-color: #374151;
}

/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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*/