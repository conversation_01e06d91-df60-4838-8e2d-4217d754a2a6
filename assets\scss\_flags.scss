// Flag icons styling for modern design
.fi {
    display: inline-block;
    width: 1.33333em;
    height: 1em;
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    position: relative;
    border-radius: 2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    
    &:hover {
        transform: scale(1.1);
    }
}

// Size variants
.fi-sm {
    width: 1em;
    height: 0.75em;
}

.fi-lg {
    width: 1.5em;
    height: 1.125em;
}

.fi-xl {
    width: 2em;
    height: 1.5em;
}

// Modern flag container
.flag-container {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

// Dark mode support for flags
[data-bs-theme="dark"] {
    .flag-container {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(255, 255, 255, 0.1);
        
        &:hover {
            background: rgba(30, 41, 59, 0.95);
        }
    }
    
    .fi {
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
}

// Language selector specific styling
.language-selector {
    .fi {
        margin-right: 0.5rem;
    }
}

// Dropdown flag styling
.dropdown-menu .fi {
    margin-right: 0.75rem;
    vertical-align: middle;
}

// Navigation flag styling
.navbar .fi {
    margin-right: 0.5rem;
}
