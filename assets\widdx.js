import './scss/widdx.scss';

import $ from 'jquery';
import * as bootstrap from 'bootstrap';
import TomSelect from 'tom-select';

globalThis.$ = globalThis.jQuery = $;
globalThis.bootstrap = bootstrap;
globalThis.TomSelect = TomSelect;

// FOSSBilling API wrapper and utilities
globalThis.FOSSBilling = {
    message: function(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) return;

        const toastId = 'toast-' + Date.now();
        const alertClass = type === 'error' ? 'bg-danger' :
                          type === 'success' ? 'bg-success' :
                          type === 'warning' ? 'bg-warning' : 'bg-info';

        const toastHtml = `
            <div id="${toastId}" class="toast ${alertClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
};

// BB utilities for backward compatibility
globalThis.bb = {
    reload: function() {
        window.location.reload();
    },
    redirect: function(url) {
        window.location.href = url;
    }
};

document.addEventListener('DOMContentLoaded', () => {
  /**
   * Enable Bootstrap Tooltip
   */
  const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
  [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

  /**
   * Initialize TomSelect for enhanced select elements
   */
  const selectElements = document.querySelectorAll('.js-language-selector, .tom-select');
  selectElements.forEach(select => {
    if (!select.tomselect) {
      new TomSelect(select, {
        plugins: ['dropdown_header'],
        render: {
          option: function(data, escape) {
            return '<div class="d-flex align-items-center">' +
              '<span class="fi fi-' + escape(data.value.split('_')[1]?.toLowerCase() || '') + ' me-2"></span>' +
              '<span>' + escape(data.text) + '</span>' +
              '</div>';
          },
          item: function(data, escape) {
            return '<div class="d-flex align-items-center">' +
              '<span class="fi fi-' + escape(data.value.split('_')[1]?.toLowerCase() || '') + ' me-2"></span>' +
              '<span>' + escape(data.text) + '</span>' +
              '</div>';
          }
        }
      });
    }
  });

  /**
   * Manage flash message to show after page reload
   */
  globalThis.flashMessage = ({message = '', reload = false, type = 'info'}) => {
    let key = 'flash-message';
    let sessionMessage = sessionStorage.getItem(key);
    if (message === '' && sessionMessage) {
      FOSSBilling.message(sessionMessage, type);
      sessionStorage.removeItem(key);
      return;
    }
    if (message) {
      sessionStorage.setItem(key, message);
      if (typeof reload === 'boolean' && reload) {
        bb.reload();
      } else if (typeof reload === 'string') {
        bb.redirect(reload);
      }
    }
  }
  flashMessage({});

  /**
   * Add asterisk to required field labels
   */
  const requiredInputs = document.querySelectorAll('input[required], textarea[required]');
  requiredInputs.forEach(input => {
    const label = input.previousElementSibling;
    const isAuth = input.parentElement.parentElement.classList.contains('auth');
    if (!isAuth && label && label.tagName.toLowerCase() === 'label') {
      const asterisk = document.createElement('span');
      asterisk.textContent = ' *';
      asterisk.classList.add('text-danger');
      label.appendChild(asterisk);
    }
  });

  /**
   * Currency selector functionality
   */
  const currencySelector = document.querySelectorAll('select.currency_selector');
  currencySelector.forEach(function (select) {
    select.addEventListener('change', function () {
      if (typeof API !== 'undefined' && API.guest) {
        API.guest.post('cart/set_currency', {currency: select.value}, function(response) {
          location.reload()
        }, function(error) {
          FOSSBilling.message(error, 'error')
        });
      }
    });
  });

  /**
   * Period selector for pricing
   */
  const periodSelector = document.getElementById('period-selector');
  if (periodSelector) {
    periodSelector.addEventListener('change', function() {
      const selectedPeriod = this.value;

      // Hide all period-specific elements
      document.querySelectorAll('.period').forEach(el => {
        el.style.display = 'none';
      });

      // Show elements for selected period
      document.querySelectorAll('.period.' + selectedPeriod).forEach(el => {
        el.style.display = 'block';
      });
    });

    // Trigger change event to show initial period
    periodSelector.dispatchEvent(new Event('change'));
  }

  /**
   * Form validation enhancements
   */
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', function(e) {
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          field.classList.add('is-invalid');
          isValid = false;
        } else {
          field.classList.remove('is-invalid');
        }
      });

      if (!isValid) {
        e.preventDefault();
        FOSSBilling.message('Please fill in all required fields', 'error');
      }
    });
  });
});

