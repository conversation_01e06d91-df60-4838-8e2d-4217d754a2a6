import './scss/widdx.scss';

import $ from 'jquery';
import * as bootstrap from 'bootstrap';
import TomSelect from 'tom-select';

globalThis.$ = globalThis.jQuery = $;
globalThis.bootstrap = bootstrap;
globalThis.TomSelect = TomSelect;

// FOSSBilling API wrapper and utilities
globalThis.FOSSBilling = {
    message: function(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) return;

        const toastId = 'toast-' + Date.now();
        const alertClass = type === 'error' ? 'bg-danger' :
                          type === 'success' ? 'bg-success' :
                          type === 'warning' ? 'bg-warning' : 'bg-info';

        const toastHtml = `
            <div id="${toastId}" class="toast ${alertClass} text-white" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
        toast.show();

        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }
};

// BB utilities for backward compatibility
globalThis.bb = {
    reload: function() {
        window.location.reload();
    },
    redirect: function(url) {
        window.location.href = url;
    }
};

document.addEventListener('DOMContentLoaded', () => {
  // Add modern CSS classes for enhanced effects on desktop
  if (window.innerWidth >= 1025) {
    document.body.classList.add('hover-effects-enabled');
  }

  // Modern page loading animation
  document.body.classList.add('animate-fade-in-up');

  /**
   * Enable Bootstrap Tooltip with modern styling
   */
  const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
  [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
    customClass: 'modern-tooltip'
  }));

  /**
   * Initialize TomSelect for enhanced select elements
   */
  const selectElements = document.querySelectorAll('.js-language-selector, .tom-select');
  selectElements.forEach(select => {
    if (!select.tomselect) {
      new TomSelect(select, {
        plugins: ['dropdown_header'],
        render: {
          option: function(data, escape) {
            return '<div class="d-flex align-items-center">' +
              '<span class="fi fi-' + escape(data.value.split('_')[1]?.toLowerCase() || '') + ' me-2"></span>' +
              '<span>' + escape(data.text) + '</span>' +
              '</div>';
          },
          item: function(data, escape) {
            return '<div class="d-flex align-items-center">' +
              '<span class="fi fi-' + escape(data.value.split('_')[1]?.toLowerCase() || '') + ' me-2"></span>' +
              '<span>' + escape(data.text) + '</span>' +
              '</div>';
          }
        }
      });
    }
  });

  /**
   * Manage flash message to show after page reload
   */
  globalThis.flashMessage = ({message = '', reload = false, type = 'info'}) => {
    let key = 'flash-message';
    let sessionMessage = sessionStorage.getItem(key);
    if (message === '' && sessionMessage) {
      FOSSBilling.message(sessionMessage, type);
      sessionStorage.removeItem(key);
      return;
    }
    if (message) {
      sessionStorage.setItem(key, message);
      if (typeof reload === 'boolean' && reload) {
        bb.reload();
      } else if (typeof reload === 'string') {
        bb.redirect(reload);
      }
    }
  }
  flashMessage({});

  /**
   * Add asterisk to required field labels
   */
  const requiredInputs = document.querySelectorAll('input[required], textarea[required]');
  requiredInputs.forEach(input => {
    const label = input.previousElementSibling;
    const isAuth = input.parentElement.parentElement.classList.contains('auth');
    if (!isAuth && label && label.tagName.toLowerCase() === 'label') {
      const asterisk = document.createElement('span');
      asterisk.textContent = ' *';
      asterisk.classList.add('text-danger');
      label.appendChild(asterisk);
    }
  });

  /**
   * Currency selector functionality
   */
  const currencySelector = document.querySelectorAll('select.currency_selector');
  currencySelector.forEach(function (select) {
    select.addEventListener('change', function () {
      if (typeof API !== 'undefined' && API.guest) {
        API.guest.post('cart/set_currency', {currency: select.value}, function(response) {
          location.reload()
        }, function(error) {
          FOSSBilling.message(error, 'error')
        });
      }
    });
  });

  /**
   * Period selector for pricing
   */
  const periodSelector = document.getElementById('period-selector');
  if (periodSelector) {
    periodSelector.addEventListener('change', function() {
      const selectedPeriod = this.value;

      // Hide all period-specific elements
      document.querySelectorAll('.period').forEach(el => {
        el.style.display = 'none';
      });

      // Show elements for selected period
      document.querySelectorAll('.period.' + selectedPeriod).forEach(el => {
        el.style.display = 'block';
      });
    });

    // Trigger change event to show initial period
    periodSelector.dispatchEvent(new Event('change'));
  }

  /**
   * Form validation enhancements
   */
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    form.addEventListener('submit', function(e) {
      const requiredFields = form.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (!field.value.trim()) {
          field.classList.add('is-invalid');
          isValid = false;
        } else {
          field.classList.remove('is-invalid');
        }
      });

      if (!isValid) {
        e.preventDefault();
        FOSSBilling.message('Please fill in all required fields', 'error');
      }
    });
  });

  /**
   * Modern theme switcher with smooth transitions
   */
  const themeToggle = document.querySelector('[data-bs-theme-toggle]');
  if (themeToggle) {
    themeToggle.addEventListener('click', function() {
      const currentTheme = document.documentElement.getAttribute('data-bs-theme');
      const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

      // Add transition class
      document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';

      // Change theme
      document.documentElement.setAttribute('data-bs-theme', newTheme);
      localStorage.setItem('theme', newTheme);

      // Update icon
      const icon = this.querySelector('i');
      if (icon) {
        icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
      }

      // Remove transition after animation
      setTimeout(() => {
        document.body.style.transition = '';
      }, 300);
    });
  }

  /**
   * Smooth scroll for anchor links
   */
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  /**
   * Modern card animations on scroll
   */
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-fade-in-up');
        observer.unobserve(entry.target);
      }
    });
  }, observerOptions);

  // Observe cards and other elements for animation
  document.querySelectorAll('.card, .alert, .list-group').forEach(el => {
    observer.observe(el);
  });

  /**
   * Enhanced form validation with modern styling
   */
  const forms = document.querySelectorAll('form');
  forms.forEach(form => {
    // Real-time validation
    const inputs = form.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
      input.addEventListener('blur', function() {
        validateField(this);
      });

      input.addEventListener('input', function() {
        if (this.classList.contains('is-invalid')) {
          validateField(this);
        }
      });
    });

    form.addEventListener('submit', function(e) {
      let isValid = true;
      inputs.forEach(input => {
        if (!validateField(input)) {
          isValid = false;
        }
      });

      if (!isValid) {
        e.preventDefault();
        FOSSBilling.message('Please correct the errors in the form', 'error');

        // Focus first invalid field
        const firstInvalid = form.querySelector('.is-invalid');
        if (firstInvalid) {
          firstInvalid.focus();
          firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    });
  });

  function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let message = '';

    // Remove existing feedback
    const existingFeedback = field.parentNode.querySelector('.invalid-feedback');
    if (existingFeedback) {
      existingFeedback.remove();
    }

    // Required field validation
    if (field.hasAttribute('required') && !value) {
      isValid = false;
      message = 'This field is required';
    }

    // Email validation
    if (field.type === 'email' && value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        isValid = false;
        message = 'Please enter a valid email address';
      }
    }

    // Password validation
    if (field.type === 'password' && value && value.length < 6) {
      isValid = false;
      message = 'Password must be at least 6 characters long';
    }

    // Update field styling
    if (isValid) {
      field.classList.remove('is-invalid');
      field.classList.add('is-valid');
    } else {
      field.classList.remove('is-valid');
      field.classList.add('is-invalid');

      // Add error message
      const feedback = document.createElement('div');
      feedback.className = 'invalid-feedback';
      feedback.textContent = message;
      field.parentNode.appendChild(feedback);
    }

    return isValid;
  }

  /**
   * Modern loading states for buttons
   */
  document.addEventListener('click', function(e) {
    if (e.target.matches('button[type="submit"], .btn-loading')) {
      const btn = e.target;
      const originalText = btn.innerHTML;

      btn.classList.add('loading');
      btn.disabled = true;
      btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Loading...';

      // Reset after form submission or timeout
      setTimeout(() => {
        btn.classList.remove('loading');
        btn.disabled = false;
        btn.innerHTML = originalText;
      }, 3000);
    }
  });

  /**
   * Auto-hide alerts after delay
   */
  document.querySelectorAll('.alert:not(.alert-permanent)').forEach(alert => {
    setTimeout(() => {
      alert.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
      alert.style.opacity = '0';
      alert.style.transform = 'translateY(-20px)';

      setTimeout(() => {
        alert.remove();
      }, 500);
    }, 5000);
  });
});

