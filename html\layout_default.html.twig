<!DOCTYPE html>
<html lang="en" data-bs-theme="{{ settings.theme|default('light') }}">
<head>
    <meta charset="utf-8">
   <title>{{ settings.meta_title_prefix }} {% block meta_title %}{% endblock %} {{ settings.meta_title_suffix }}</title>

    <meta property="bb:url" content="{{ constant('SYSTEM_URL') }}">
    <meta property="bb:client_area" content="{{ '/'|link }}">
    <meta name="csrf-token" content="{{ CSRFToken }}">

    <meta name="description" content="{% block meta_description %}{{ settings.meta_description }}{% endblock %}">
    <meta name="keywords" content="{{ settings.meta_keywords }}">
    <meta name="robots" content="{{ settings.meta_robots }}">
    <meta name="author" content="{{ settings.meta_author }}">
    <meta name="generator" content="FOSSBilling">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {% block opengraph %}{% endblock %}
    <link rel='stylesheet' type='text/css' href="{{ 'css/font-awesome.css' | asset_url }}">
    {{ encore_entry_link_tags('widdx') }}
    <link rel="shortcut icon" href="{{ guest.system_company.favicon_url }}">

    {{ "Api/API.js" | library_url | script_tag }}
    {{ encore_entry_script_tags('widdx') }}

    {{ DebugBar_renderHead() }}

    {% block head %}{% endblock %}
    {% block js %}{% endblock %}
</head>

<body class="{% block body_class %}{% endblock %}">

{% block body %}
{% if not client and settings.require_login %}
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            const publicPaths = [
                '/news',
                '/tos',
                '/privacy-policy'
            ];
            const currentPath = window.location.pathname;

            const isAllowed = publicPaths.some(path => currentPath.startsWith(path));
            if (!isAllowed) {
                window.location.href = '{{ "login"|link }}';
            }
        });
    </script>
{% endif %}

{% if client %}
    {% set profile = client.profile_get %}
{% endif %}
{% set company = guest.system_company %}

{% if settings.theme == 'dark' %}
    {% set logo_url =  company.logo_url_dark %}
{% else %}
    {% set logo_url =  company.logo_url %}
{% endif %}
<div class="container">
    <header>
        <nav class="navbar navbar-expand-md py-4 shadow-soft">
            <div class="container-fluid">
                {% if logo_url and settings.show_company_logo %}
                    <a class="navbar-brand hover-scale" href="{{ '/'|link }}">
                        <img class="d-none d-sm-block" src="{{ logo_url }}" alt="{{ company.name }}" height="45px"
                             title="{{ company.name }}" style="transition: transform 0.3s ease;">
                        <span class="d-sm-none fw-bold">{{ company.name }}</span>
                    </a>
                {% else %}
                    <a class="navbar-brand hover-scale" href="{{ '/'|link }}">
                        <span class="fw-bold bg-gradient-primary bg-clip-text">{{ company.name }}</span>
                    </a>
                {% endif %}

                <!-- Theme Toggle Button -->
                <button class="btn btn-outline-secondary d-md-none me-2" type="button" data-bs-theme-toggle aria-label="{{ 'Toggle theme'|trans }}">
                    <i class="fas fa-moon"></i>
                </button>

                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="{{ 'Toggle navigation'|trans }}">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <div class="navbar-nav me-auto mb-2 mb-lg-0 w-100 d-flex justify-content-end align-items-center">

                        <!-- Theme Toggle for Desktop -->
                        <li class="nav-item d-none d-md-block me-3">
                            <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-theme-toggle aria-label="{{ 'Toggle theme'|trans }}">
                                <i class="fas fa-moon"></i>
                            </button>
                        </li>

                        {% set languages = guest.extension_languages(true) %}
                        {% if languages|length > 1 %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="flag-container">
                                        <span class="fi fi-{{ guest.system_locale|split('_')[1]|lower }}"></span>
                                        <span class="d-none d-lg-inline">{{ guest.system_locale|split('_')[0]|upper }}</span>
                                    </div>
                                </a>
                                <ul class="dropdown-menu shadow-soft">
                                    {% for lang in languages %}
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center" href="{{ ('client/change_language')|link({'locale': lang.locale}) }}">
                                                <span class="fi fi-{{ lang.locale|split('_')[1]|lower }} me-2"></span>
                                                <span>{{ lang.title }}</span>
                                            </a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </li>
                        {% endif %}

                        {% if settings.top_menu_dashboard %}
                            <li class="nav-item d-none d-md-block">
                                <a class="nav-link px-3 py-2 rounded-pill hover-lift" href="{{ ''|link }}">
                                    <i class="fas fa-tachometer-alt me-1"></i>{{ 'Dashboard'|trans }}
                                </a>
                            </li>
                        {% endif %}

                        {% if settings.top_menu_order %}
                            <li class="nav-item d-none d-md-block">
                                <a class="nav-link px-3 py-2 rounded-pill hover-lift" href="{{ '/order'|link }}">
                                    <i class="fas fa-shopping-cart me-1"></i>{{ 'Order'|trans }}
                                </a>
                            </li>
                        {% endif %}

                        {% if settings.top_menu_profile %}
                            <li class="nav-item d-none d-md-block">
                                {% if not client %}
                                    <a class="nav-link px-3 py-2 rounded-pill hover-lift" href="{{ 'login'|link }}">
                                        <i class="fas fa-sign-in-alt me-1"></i>{{ 'Login'|trans }}
                                    </a>
                                {% endif %}
                            </li>
                        {% endif %}

                        {% if settings.top_menu_signout %}
                            <li class="nav-item d-none d-md-block">
                                {% if client %}
                                    <div class="dropdown">
                                        <button class="btn btn-outline-primary dropdown-toggle d-flex align-items-center shadow-soft hover-lift" type="button" data-bs-toggle="dropdown" aria-expanded="false" aria-label="{{ 'User menu'|trans }}">
                                            <img class="img-fluid rounded-circle me-2 shadow-sm" alt="{{ profile.first_name }} {{ profile.last_name }} gravatar" src="{{ profile.email|gravatar(60) }}" height="32px" width="32px">
                                            <div class="text-start">
                                                <div class="fw-semibold fs-7">
                                                    {% if profile.company %}
                                                        {{ profile.first_name }} {{ profile.last_name }}
                                                    {% else %}
                                                        {{ profile.first_name }} {{ profile.last_name }}
                                                    {% endif %}
                                                </div>
                                                {% if profile.company %}
                                                    <div class="text-muted fs-8">{{ profile.company }}</div>
                                                {% endif %}
                                            </div>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end shadow-soft-lg">
                                            <li><a class="dropdown-item d-flex align-items-center" href="{{ 'client/profile'|link }}">
                                                <i class="fas fa-user me-3 text-primary"></i>
                                                <div>
                                                    <div class="fw-semibold">{{ 'Profile'|trans }}</div>
                                                    <div class="text-muted fs-8">{{ 'Manage your account'|trans }}</div>
                                                </div>
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item d-flex align-items-center text-danger" href="{{ 'client/logout'|link }}">
                                                <i class="fas fa-sign-out-alt me-3"></i>
                                                <div>
                                                    <div class="fw-semibold">{{ 'Sign out'|trans }}</div>
                                                    <div class="text-muted fs-8">{{ 'End your session'|trans }}</div>
                                                </div>
                                            </a></li>
                                        </ul>
                                    </div>
                                {% else %}
                                    <a class="ms-2 btn btn-primary shadow-soft hover-lift d-none d-md-block"
                                       href="{{ 'signup'|link }}" aria-label="{{ 'Register for an account'|trans }}">{{ 'Register'|trans }}</a>
                                {% endif %}
                            </li>
                        {% endif %}

                        <div class="d-md-none">
                            {{ include('mobile_menu.html.twig') }}
                        </div>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    <div class="container-fluid px-4">
        <div class="row g-4">
        {% if client or not settings.hide_menu %}
            <div class="col-3 d-none d-md-block">
                <div class="sticky-top" style="top: 2rem;">
                    {{ include('partial_menu.html.twig') }}
                </div>
            </div>
        {% endif %}
            <div class="col-12 col-md-9" {% if not client and settings.hide_menu %} style="margin-left: auto; margin-right: auto;" {% endif %}>
                <div id="wrapper" class="animate-fade-in-up">
                    <section role="main">
                        {% block content_before %}{% endblock %}
                        <div class="content-block" role="main">
                            {% if settings.show_breadcrumb %}
                                {% block breadcrumbs %}
                                    <nav aria-label="{{ 'breadcrumb'|trans }}" class="mb-4">
                                        <ol class="breadcrumb d-none d-md-flex bg-transparent p-0 mb-0">
                                            <li class="breadcrumb-item">
                                                <a href="{{ '/'|link }}" class="text-decoration-none">
                                                    <i class="fas fa-home me-1"></i>{{ 'Home'|trans }}
                                                </a>
                                            </li>
                                            {% block breadcrumb %}
                                                <li class="active breadcrumb-item text-primary fw-semibold">{{ 'Dashboard'|trans }}</li>
                                            {% endblock %}
                                        </ol>
                                    </nav>
                                {% endblock %}
                            {% endif %}

                            {% block content %}{% endblock %}

                            {{ include('partial_message.html.twig') }}

                            {% block content_after %}{% endblock %}
                        </div>
                    </section>
                    <div id="push"></div>
                </div>

                {% if settings.footer_enabled %}
                    <footer id="footer" class="mt-5 py-4 border-top">
                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-md-6 text-center text-md-start">
                                    <div class="text-muted">
                                        <i class="fas fa-copyright me-1"></i>
                                        {{ now|format_date(pattern='yyyy') }} {{ settings.footer_signature | default(company.signature) }}
                                    </div>
                                    {% if company.bank_info_pagebottom == 1 %}
                                        <div class="mt-2 small text-muted">
                                            <strong>{{'Payment Information'|trans}}</strong><br>
                                            <span class="me-3"><i class="fas fa-university me-1"></i>{{'Bank Name'|trans}}: {{ company.bank_name }}</span>
                                            <span class="me-3"><i class="fas fa-code me-1"></i>{{'BIC / SWIFT Code'|trans}}: {{ company.bic }}</span>
                                            <span><i class="fas fa-credit-card me-1"></i>{{'Account Number'|trans}}: {{ company.account_number }}</span>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 text-center text-md-end mt-3 mt-md-0">
                                    <div class="d-flex justify-content-center justify-content-md-end flex-wrap gap-3">
                                        {% if settings.footer_link_1_enabled %}
                                            {% if 'http://' in settings.footer_link_1_page or  'https://' in settings.footer_link_1_page %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_1_page }}">{{ settings.footer_link_1_title }}</a>
                                            {% else %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_1_page | link }}">{{ settings.footer_link_1_title }}</a>
                                            {% endif %}
                                        {% endif %}
                                        {% if settings.footer_link_2_enabled %}
                                            {% if 'http://' in settings.footer_link_2_page or  'https://' in settings.footer_link_2_page %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_2_page }}">{{ settings.footer_link_2_title }}</a>
                                            {% else %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_2_page | link }}">{{ settings.footer_link_2_title }}</a>
                                            {% endif %}
                                        {% endif %}
                                        {% if settings.footer_link_3_enabled %}
                                            {% if 'http://' in settings.footer_link_3_page or  'https://' in settings.footer_link_3_page %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_3_page }}">{{ settings.footer_link_3_title }}</a>
                                            {% else %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_3_page | link }}">{{ settings.footer_link_3_title }}</a>
                                            {% endif %}
                                        {% endif %}
                                        {% if settings.footer_link_4_enabled %}
                                            {% if 'http://' in settings.footer_link_4_page or  'https://' in settings.footer_link_4_page %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_4_page }}">{{ settings.footer_link_4_title }}</a>
                                            {% else %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_4_page | link }}">{{ settings.footer_link_4_title }}</a>
                                            {% endif %}
                                        {% endif %}
                                        {% if settings.footer_link_5_enabled %}
                                            {% if 'http://' in settings.footer_link_5_page or  'https://' in settings.footer_link_5_page %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_5_page }}">{{ settings.footer_link_5_title }}</a>
                                            {% else %}
                                                <a class="text-decoration-none text-muted hover-primary"
                                                   href="{{ settings.footer_link_5_page | link }}">{{ settings.footer_link_5_title }}</a>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% if guest.extension_is_on({"mod":'branding'}) %}
                                <div class="text-center mt-3 pt-3 border-top">
                                    <small class="text-muted">
                                        <span>{{ 'Powered by the'|trans }}&nbsp;</span>
                                        <a class="text-decoration-none text-primary fw-semibold"
                                            href="https://fossbilling.org" title="Billing Software"
                                           target="_blank">{{ 'FOSSBilling Community'|trans }}</a>
                                    </small>
                                </div>
                            {% endif %}
                        </div>
                    </footer>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1070;"></div>
    {% if settings.footer_to_top_enabled %}
        <a href="#top" class="position-fixed btn btn-primary shadow-soft hover-lift"
           style="bottom: 2rem; right: 2rem; border-radius: 50%; width: 3rem; height: 3rem; display: flex; align-items: center; justify-content: center; z-index: 1000;"
           aria-label="{{ 'Back to top'|trans }}">
            <i class="fas fa-arrow-up"></i>
        </a>
    {% endif %}
    <div class="wait" style="display:none" onclick="$(this).hide();">
        <div class="d-flex align-items-center justify-content-center position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-50" style="z-index: 9999;">
            <div class="card shadow-soft-xl border-0">
                <div class="card-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" style="width: 3rem; height: 3rem;" role="status">
                        <span class="visually-hidden">{{ 'Loading...'|trans }}</span>
                    </div>
                    <div class="fw-semibold text-muted">{{ 'Please wait...'|trans }}</div>
                </div>
            </div>
        </div>
    </div>
    <noscript>{{ "NOTE: Many features on FOSSBilling require Javascript and cookies. You can enable both in your browser's preference settings."|trans }}</noscript>

    {% endblock %}

    {% if settings.inject_javascript %}
        {{ settings.inject_javascript | raw }}
    {% endif %}
    {{ include('partial_pending_messages.html.twig', ignore_missing: true) }}
    {% if guest.extension_is_on({ "mod": "cookieconsent" }) %}
        {{ include('mod_cookieconsent_index.html.twig', ignore_missing: true) }}
    {% endif %}
</div>
{{ DebugBar_render() }}
</body>
</html>
