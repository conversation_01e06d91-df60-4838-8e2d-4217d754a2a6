{# Modern Card Component Template #}
{# Usage: {{ include('partial_modern_card.html.twig', {
    'title': 'Card Title',
    'content': 'Card content here',
    'icon': 'fas fa-icon',
    'variant': 'primary|secondary|success|warning|danger',
    'actions': [
        {'text': 'Action 1', 'url': '/action1', 'class': 'btn-primary'},
        {'text': 'Action 2', 'url': '/action2', 'class': 'btn-outline-secondary'}
    ]
}) }} #}

{% set card_variant = variant|default('primary') %}
{% set card_icon = icon|default('fas fa-info-circle') %}
{% set card_title = title|default('Card Title') %}
{% set card_content = content|default('') %}
{% set card_actions = actions|default([]) %}

<div class="card hover-lift shadow-soft animate-fade-in-up">
    {% if card_title %}
        <div class="card-header bg-gradient-{{ card_variant }} text-white border-0">
            <div class="d-flex align-items-center">
                <i class="{{ card_icon }} me-2 fs-5"></i>
                <h5 class="card-title mb-0 fw-semibold">{{ card_title }}</h5>
            </div>
        </div>
    {% endif %}
    
    {% if card_content %}
        <div class="card-body">
            {{ card_content|raw }}
        </div>
    {% endif %}
    
    {% if card_actions|length > 0 %}
        <div class="card-footer bg-transparent border-0 pt-0">
            <div class="d-flex gap-2 flex-wrap">
                {% for action in card_actions %}
                    <a href="{{ action.url }}" class="btn {{ action.class|default('btn-primary') }} btn-sm hover-lift">
                        {% if action.icon %}
                            <i class="{{ action.icon }} me-1"></i>
                        {% endif %}
                        {{ action.text }}
                    </a>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>
