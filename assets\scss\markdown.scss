// Modern Markdown Styles
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

// Variables
$primary: #6366f1;
$secondary: #8b5cf6;
$success: #10b981;
$warning: #f59e0b;
$danger: #ef4444;
$dark: #0f172a;
$light: #f8fafc;

$border-radius: 0.75rem;
$border-radius-sm: 0.5rem;
$box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);

// Modern markdown container
.markdown-body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 1rem;
    line-height: 1.7;
    color: #374151;
    max-width: none;
    
    // Headings
    h1, h2, h3, h4, h5, h6 {
        font-weight: 600;
        line-height: 1.3;
        margin-top: 2rem;
        margin-bottom: 1rem;
        color: #111827;
        
        &:first-child {
            margin-top: 0;
        }
    }
    
    h1 {
        font-size: 2.25rem;
        border-bottom: 3px solid $primary;
        padding-bottom: 0.5rem;
        margin-bottom: 1.5rem;
    }
    
    h2 {
        font-size: 1.875rem;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.5rem;
    }
    
    h3 {
        font-size: 1.5rem;
        color: $primary;
    }
    
    h4 {
        font-size: 1.25rem;
    }
    
    h5 {
        font-size: 1.125rem;
    }
    
    h6 {
        font-size: 1rem;
        color: #6b7280;
    }
    
    // Paragraphs
    p {
        margin-bottom: 1.25rem;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    // Links
    a {
        color: $primary;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
            color: darken($primary, 10%);
            text-decoration: underline;
            text-decoration-color: $primary;
            text-decoration-thickness: 2px;
            text-underline-offset: 3px;
        }
    }
    
    // Lists
    ul, ol {
        margin-bottom: 1.25rem;
        padding-left: 1.5rem;
        
        li {
            margin-bottom: 0.5rem;
            
            &::marker {
                color: $primary;
            }
        }
        
        ul, ol {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }
    }
    
    // Code
    code {
        background: #f3f4f6;
        color: #dc2626;
        padding: 0.125rem 0.375rem;
        border-radius: $border-radius-sm;
        font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    pre {
        background: #1f2937;
        color: #f9fafb;
        padding: 1.5rem;
        border-radius: $border-radius;
        overflow-x: auto;
        margin: 1.5rem 0;
        box-shadow: $box-shadow;
        
        code {
            background: transparent;
            color: inherit;
            padding: 0;
            border-radius: 0;
            font-size: 0.875rem;
        }
    }
    
    // Blockquotes
    blockquote {
        border-left: 4px solid $primary;
        background: linear-gradient(135deg, rgba($primary, 0.05) 0%, rgba($secondary, 0.05) 100%);
        padding: 1rem 1.5rem;
        margin: 1.5rem 0;
        border-radius: 0 $border-radius $border-radius 0;
        font-style: italic;
        
        p {
            margin-bottom: 0;
            font-size: 1.125rem;
            color: #4b5563;
        }
        
        &::before {
            content: '"';
            font-size: 3rem;
            color: $primary;
            opacity: 0.3;
            line-height: 1;
            float: left;
            margin-right: 0.5rem;
            margin-top: -0.5rem;
        }
    }
    
    // Tables
    table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;
        border-radius: $border-radius;
        overflow: hidden;
        box-shadow: $box-shadow;
        
        thead {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            
            th {
                padding: 1rem;
                text-align: left;
                font-weight: 600;
                color: #374151;
                border: none;
                font-size: 0.875rem;
                text-transform: uppercase;
                letter-spacing: 0.05em;
            }
        }
        
        tbody {
            tr {
                transition: background-color 0.3s ease;
                
                &:hover {
                    background: rgba($primary, 0.02);
                }
                
                &:nth-child(even) {
                    background: #f9fafb;
                }
            }
            
            td {
                padding: 1rem;
                border: none;
                border-bottom: 1px solid #e5e7eb;
                vertical-align: top;
                
                &:last-child {
                    border-bottom: none;
                }
            }
        }
    }
    
    // Images
    img {
        max-width: 100%;
        height: auto;
        border-radius: $border-radius;
        box-shadow: $box-shadow;
        margin: 1rem 0;
        
        &.img-fluid {
            border-radius: $border-radius;
        }
    }
    
    // Horizontal rules
    hr {
        border: none;
        height: 2px;
        background: linear-gradient(90deg, transparent, $primary, transparent);
        margin: 2rem 0;
    }
    
    // Task lists
    .task-list-item {
        list-style: none;
        margin-left: -1.5rem;
        
        input[type="checkbox"] {
            margin-right: 0.5rem;
            transform: scale(1.2);
            accent-color: $primary;
        }
    }
}

// Dark mode support
[data-bs-theme="dark"] {
    .markdown-body {
        color: #e5e7eb;
        
        h1, h2, h3, h4, h5, h6 {
            color: #f9fafb;
        }
        
        h6 {
            color: #9ca3af;
        }
        
        code {
            background: #374151;
            color: #fbbf24;
        }
        
        blockquote {
            background: linear-gradient(135deg, rgba($primary, 0.1) 0%, rgba($secondary, 0.1) 100%);
            
            p {
                color: #d1d5db;
            }
        }
        
        table {
            thead {
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
                
                th {
                    color: #e5e7eb;
                }
            }
            
            tbody {
                tr:nth-child(even) {
                    background: #1f2937;
                }
                
                tr:hover {
                    background: rgba($primary, 0.1);
                }
                
                td {
                    border-bottom-color: #374151;
                }
            }
        }
    }
}
