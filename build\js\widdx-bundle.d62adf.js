/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/widdx.js":
/*!*************************!*\
  !*** ./assets/widdx.js ***!
  \*************************/
/***/ (() => {

throw new Error("Module build failed (from ./node_modules/babel-loader/lib/index.js):\nSyntaxError: C:\\xampp\\htdocs\\themes\\widdx\\assets\\widdx.js: Identifier 'forms' has already been declared. (269:8)\n\n\u001b[0m \u001b[90m 267 |\u001b[39m \u001b[90m   * Enhanced form validation with modern styling\u001b[39m\n \u001b[90m 268 |\u001b[39m \u001b[90m   */\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 269 |\u001b[39m   \u001b[36mconst\u001b[39m forms \u001b[33m=\u001b[39m document\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'form'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m         \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 270 |\u001b[39m   forms\u001b[33m.\u001b[39mforEach(form \u001b[33m=>\u001b[39m {\n \u001b[90m 271 |\u001b[39m     \u001b[90m// Real-time validation\u001b[39m\n \u001b[90m 272 |\u001b[39m     \u001b[36mconst\u001b[39m inputs \u001b[33m=\u001b[39m form\u001b[33m.\u001b[39mquerySelectorAll(\u001b[32m'input, textarea, select'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n    at constructor (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:367:19)\n    at Parser.raise (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:6627:19)\n    at ScopeHandler.checkRedeclarationInScope (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:1644:19)\n    at ScopeHandler.declareName (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:1610:12)\n    at Parser.declareNameFromIdentifier (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:7591:16)\n    at Parser.checkIdentifier (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:7587:12)\n    at Parser.checkLVal (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:7526:12)\n    at Parser.parseVarId (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:13412:10)\n    at Parser.parseVar (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:13383:12)\n    at Parser.parseVarStatement (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:13230:10)\n    at Parser.parseStatementContent (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12851:23)\n    at Parser.parseStatementLike (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12767:17)\n    at Parser.parseStatementListItem (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12747:17)\n    at Parser.parseBlockOrModuleBlockBody (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:13316:61)\n    at Parser.parseBlockBody (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:13309:10)\n    at Parser.parseBlock (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:13297:10)\n    at Parser.parseFunctionBody (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12101:24)\n    at Parser.parseArrowExpression (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12076:10)\n    at Parser.parseParenAndDistinguishExpression (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11686:12)\n    at Parser.parseExprAtom (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11326:23)\n    at Parser.parseExprSubscripts (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11076:23)\n    at Parser.parseUpdate (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11061:21)\n    at Parser.parseMaybeUnary (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11041:23)\n    at Parser.parseMaybeUnaryOrPrivate (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10894:61)\n    at Parser.parseExprOps (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10899:23)\n    at Parser.parseMaybeConditional (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10876:23)\n    at Parser.parseMaybeAssign (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10826:21)\n    at C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10795:39\n    at Parser.allowInAnd (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12427:12)\n    at Parser.parseMaybeAssignAllowIn (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10795:17)\n    at Parser.parseMaybeAssignAllowInOrVoidPattern (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12494:17)\n    at Parser.parseExprListItem (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12176:18)\n    at Parser.parseCallExpressionArguments (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11247:22)\n    at Parser.parseCoverCallAndAsyncArrowHead (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11181:29)\n    at Parser.parseSubscript (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11115:19)\n    at Parser.parseSubscripts (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11089:19)\n    at Parser.parseExprSubscripts (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11080:17)\n    at Parser.parseUpdate (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11061:21)\n    at Parser.parseMaybeUnary (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:11041:23)\n    at Parser.parseMaybeUnaryOrPrivate (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10894:61)\n    at Parser.parseExprOps (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10899:23)\n    at Parser.parseMaybeConditional (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10876:23)\n    at Parser.parseMaybeAssign (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10826:21)\n    at Parser.parseExpressionBase (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10779:23)\n    at C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10775:39\n    at Parser.allowInAnd (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12422:16)\n    at Parser.parseExpression (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:10775:17)\n    at Parser.parseStatementContent (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12895:23)\n    at Parser.parseStatementLike (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12767:17)\n    at Parser.parseModuleItem (C:\\xampp\\htdocs\\themes\\widdx\\node_modules\\@babel\\parser\\lib\\index.js:12744:17)");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module doesn't tell about it's top-level declarations so it can't be inlined
/******/ 	var __webpack_exports__ = {};
/******/ 	__webpack_modules__["./assets/widdx.js"]();
/******/ 	
/******/ })()
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoianMvd2lkZHgtYnVuZGxlLmQ2MmFkZi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O1VDQUE7VUFDQTtVQUNBO1VBQ0E7VUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3dpZGR4L3dlYnBhY2svYmVmb3JlLXN0YXJ0dXAiLCJ3ZWJwYWNrOi8vd2lkZHgvd2VicGFjay9zdGFydHVwIiwid2VicGFjazovL3dpZGR4L3dlYnBhY2svYWZ0ZXItc3RhcnR1cCJdLCJzb3VyY2VzQ29udGVudCI6WyIiLCIvLyBzdGFydHVwXG4vLyBMb2FkIGVudHJ5IG1vZHVsZSBhbmQgcmV0dXJuIGV4cG9ydHNcbi8vIFRoaXMgZW50cnkgbW9kdWxlIGRvZXNuJ3QgdGVsbCBhYm91dCBpdCdzIHRvcC1sZXZlbCBkZWNsYXJhdGlvbnMgc28gaXQgY2FuJ3QgYmUgaW5saW5lZFxudmFyIF9fd2VicGFja19leHBvcnRzX18gPSB7fTtcbl9fd2VicGFja19tb2R1bGVzX19bXCIuL2Fzc2V0cy93aWRkeC5qc1wiXSgpO1xuIiwiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9