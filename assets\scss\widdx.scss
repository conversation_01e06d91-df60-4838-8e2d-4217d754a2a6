$primary: #005fc5;
$warning: #f59e0b;
$danger: #dc2626;
$body-bg: #f5f5f5;

$accordion-button-active-bg: rgba($primary, .05);
$accordion-button-focus-border-color: rgba($primary, .08);

@import "~bootstrap/scss/bootstrap";
@import "~tom-select/dist/css/tom-select.bootstrap5.css";
@import "flags";

html,
body {
    width: 100%;
    height: 100%;
}

body {
    font-family: Verdana, sans-serif;
}

.fs-7 {
    font-size: .875rem;
}

.offcanvas-item {
    border-bottom: 1px rgba(#000, .1) solid;

    &:first-child {
        border-top: 1px rgba(#000, .1) solid;
    }

    &:hover {
        background: #fafafa;
    }

    >a {
        color: $secondary;
    }
}

.card-header h1, .page-password-reset .card h1, .page-login .card h1, .page-signup .card h1 {
    font-size: 1.25rem;
}

.card-body {
    @at-root table {
        >thead {
            >tr {
                >th {
                    font-size: $font-size-sm * .875;
                    color: $secondary;
                    font-weight: 600;
                    text-transform: uppercase;
                }
            }
        }

        >tbody {
            >tr {
                >td {
                    vertical-align: middle;
                }
            }
        }
    }
}

.svg-icon {
    height: 20px;
    width: 20px;
    fill: currentColor;
}

// Remove order management accordion icons
#orderManager {
    --#{$prefix}accordion-btn-icon: none;
    --#{$prefix}accordion-btn-active-icon: none;
}

// TomSelect dropdown
.ts-input {
    color: inherit;
}

.ts-wrapper {
    &.form-select {
        background-color: inherit;
    }

    .ts-control {
        font-size: 14px;
        width: 100%;
        color: inherit;

        &.locale {
            width: 205px;
        }

        .dropdown-menu {
            width: 100%;
            height: auto;
        }
    }

    .ts-dropdown {
        font-size: 14px;

        .active {
            background-color: rgba($secondary, .06);
            color: inherit;
        }

        .selected {
            background-color: rgba($secondary, .1);
        }
    }
}

@media (max-width: 768px) {
    .ts-wrapper.js-language-selector {
        width: 180px
    }
}

.invoice-gateway {
    border: 1px $border-color solid;

    &:hover {
        border: 1px $primary solid;
    }
}

.email-list {
    border-radius: 0;

    .list-group-item {
        border: 0;
        border-bottom: 1px solid var(--bs-border-color);
        border-left: 2px solid transparent;

        &.active {
            border-left: 2px solid var(--bs-primary-border-subtle);
            background: rgba($primary, .1);
            color: $dark;
        }
    }
}

// Toast container positioning
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1055;
}

// Improved form validation styles
.is-invalid {
    border-color: $danger !important;
    box-shadow: 0 0 0 0.2rem rgba($danger, 0.25) !important;
}

// Better mobile navigation
@media (max-width: 991.98px) {
    .navbar-nav {
        padding-top: 1rem;

        .nav-item {
            margin-bottom: 0.5rem;
        }
    }

    .offcanvas-body {
        padding: 1rem;
    }
}

// Pricing component improvements
.pricing-section {
    .h4 {
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .table {
        margin-bottom: 0;

        td {
            padding: 0.5rem;
            border-top: none;
        }
    }
}

// Better button spacing
.d-grid {
    .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
    }
}

// Improved card styling
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);

    .card-header {
        background-color: rgba($primary, 0.05);
        border-bottom: 1px solid rgba($primary, 0.125);
    }
}

// Alert improvements
.alert {
    border: none;
    border-radius: 0.5rem;

    .btn-close {
        padding: 0.75rem;
    }
}

// Footer improvements
footer {
    margin-top: auto;

    .link-offset-2 {
        transition: all 0.2s ease-in-out;

        &:hover {
            transform: translateY(-1px);
        }
    }
}

// Accessibility improvements
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

// TODO: Add custom color themes to widdx. Bootstrap has a great example here: https://getbootstrap.com/docs/5.3/customize/color-modes/#custom-color-modes
