// Modern Color Palette
$primary: #6366f1;
$secondary: #8b5cf6;
$success: #10b981;
$info: #06b6d4;
$warning: #f59e0b;
$danger: #ef4444;
$light: #f8fafc;
$dark: #0f172a;

// Custom colors for modern design
$gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

// Background colors
$body-bg: #f8fafc;
$body-bg-dark: #0f172a;

// Typography
$font-family-sans-serif: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-monospace: 'JetBrains Mono', 'Fira Code', <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>, Courier, monospace;

$font-size-base: 0.95rem;
$line-height-base: 1.6;

// Spacing
$spacer: 1rem;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
  6: $spacer * 4,
  7: $spacer * 5,
);

// Border radius
$border-radius: 0.75rem;
$border-radius-sm: 0.5rem;
$border-radius-lg: 1rem;
$border-radius-xl: 1.5rem;

// Shadows
$box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Component customizations
$accordion-button-active-bg: rgba($primary, .08);
$accordion-button-focus-border-color: rgba($primary, .25);

$card-border-radius: $border-radius;
$card-box-shadow: $box-shadow;

$btn-border-radius: $border-radius-sm;
$btn-font-weight: 500;

$input-border-radius: $border-radius-sm;
$input-focus-border-color: $primary;

@import "~bootstrap/scss/bootstrap";
@import "~tom-select/dist/css/tom-select.bootstrap5.css";
@import "~flag-icons/css/flag-icons.min.css";
@import "flags";

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

html,
body {
    width: 100%;
    height: 100%;
}

body {
    font-family: $font-family-sans-serif;
    font-weight: 400;
    line-height: $line-height-base;
    background: $body-bg;
    transition: background-color 0.3s ease;
}

// Modern utility classes
.fs-7 {
    font-size: .875rem;
}

.fs-8 {
    font-size: .75rem;
}

// Gradient backgrounds
.bg-gradient-primary {
    background: $gradient-primary !important;
}

.bg-gradient-secondary {
    background: $gradient-secondary !important;
}

.bg-gradient-success {
    background: $gradient-success !important;
}

// Modern glass effect
.glass {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

// Enhanced shadows
.shadow-soft {
    box-shadow: $box-shadow !important;
}

.shadow-soft-lg {
    box-shadow: $box-shadow-lg !important;
}

.shadow-soft-xl {
    box-shadow: $box-shadow-xl !important;
}

// Modern animations
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

// Hover effects
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
        transform: translateY(-5px);
        box-shadow: $box-shadow-lg;
    }
}

.hover-scale {
    transition: transform 0.3s ease;
    
    &:hover {
        transform: scale(1.05);
    }
}

// Modern navbar
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    
    .navbar-brand {
        font-weight: 600;
        font-size: 1.25rem;
    }
    
    .nav-link {
        font-weight: 500;
        transition: color 0.3s ease;
        
        &:hover {
            color: $primary !important;
        }
    }
}

// Modern cards
.card {
    border: none;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    
    &:hover {
        transform: translateY(-2px);
        box-shadow: $box-shadow-lg;
    }
    
    .card-header {
        background: linear-gradient(135deg, rgba($primary, 0.05) 0%, rgba($secondary, 0.05) 100%);
        border-bottom: 1px solid rgba($primary, 0.1);
        border-radius: $border-radius $border-radius 0 0;
        
        h1, h2, h3, h4, h5, h6 {
            margin-bottom: 0;
            font-weight: 600;
        }
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

// Modern buttons
.btn {
    border-radius: $border-radius-sm;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    
    &.btn-primary {
        background: $gradient-primary;
        box-shadow: 0 4px 15px 0 rgba($primary, 0.3);
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba($primary, 0.4);
        }
    }
    
    &.btn-secondary {
        background: $gradient-secondary;
        box-shadow: 0 4px 15px 0 rgba($secondary, 0.3);
        
        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba($secondary, 0.4);
        }
    }
    
    &.btn-outline-primary {
        border: 2px solid $primary;
        color: $primary;
        background: transparent;
        
        &:hover {
            background: $primary;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba($primary, 0.4);
        }
    }
}

// Modern forms
.form-control, .form-select {
    border-radius: $border-radius-sm;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    
    &:focus {
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba($primary, 0.1);
    }
}

.form-label {
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

// Modern tables
.table {
    border-radius: $border-radius;
    overflow: hidden;
    box-shadow: $box-shadow;
    
    thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.05em;
        color: #64748b;
        padding: 1rem;
    }
    
    tbody td {
        border: none;
        padding: 1rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f5f9;
    }
    
    tbody tr:hover {
        background: rgba($primary, 0.02);
    }
}

// Modern alerts
.alert {
    border: none;
    border-radius: $border-radius;
    padding: 1rem 1.5rem;
    border-left: 4px solid;

    &.alert-primary {
        background: rgba($primary, 0.1);
        border-left-color: $primary;
        color: darken($primary, 20%);
    }

    &.alert-success {
        background: rgba($success, 0.1);
        border-left-color: $success;
        color: darken($success, 20%);
    }

    &.alert-warning {
        background: rgba($warning, 0.1);
        border-left-color: $warning;
        color: darken($warning, 20%);
    }

    &.alert-danger {
        background: rgba($danger, 0.1);
        border-left-color: $danger;
        color: darken($danger, 20%);
    }
}

// Modern badges
.badge {
    border-radius: $border-radius-sm;
    font-weight: 500;
    padding: 0.5rem 0.75rem;

    &.badge-primary {
        background: $gradient-primary;
    }

    &.badge-success {
        background: $gradient-success;
    }
}

// Modern breadcrumbs
.breadcrumb {
    background: transparent;
    padding: 0;
    margin-bottom: 1.5rem;

    .breadcrumb-item {
        font-weight: 500;

        &.active {
            color: $primary;
        }

        a {
            color: #64748b;
            text-decoration: none;
            transition: color 0.3s ease;

            &:hover {
                color: $primary;
            }
        }
    }
}

// Modern pagination
.pagination {
    .page-link {
        border: none;
        border-radius: $border-radius-sm;
        margin: 0 0.25rem;
        color: #64748b;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            background: $primary;
            color: white;
            transform: translateY(-2px);
        }
    }

    .page-item.active .page-link {
        background: $gradient-primary;
        border: none;
        box-shadow: 0 4px 15px 0 rgba($primary, 0.3);
    }
}

// Modern dropdowns
.dropdown-menu {
    border: none;
    border-radius: $border-radius;
    box-shadow: $box-shadow-lg;
    padding: 0.5rem;

    .dropdown-item {
        border-radius: $border-radius-sm;
        padding: 0.75rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;

        &:hover {
            background: rgba($primary, 0.1);
            color: $primary;
            transform: translateX(5px);
        }
    }
}

// Modern modals
.modal-content {
    border: none;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-xl;

    .modal-header {
        border-bottom: 1px solid #f1f5f9;
        padding: 1.5rem;

        .modal-title {
            font-weight: 600;
        }
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        border-top: 1px solid #f1f5f9;
        padding: 1.5rem;
    }
}

// Modern toasts
.toast {
    border: none;
    border-radius: $border-radius;
    box-shadow: $box-shadow-lg;

    .toast-header {
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        font-weight: 600;
    }
}

// Modern progress bars
.progress {
    border-radius: $border-radius;
    height: 0.75rem;
    background: #f1f5f9;

    .progress-bar {
        background: $gradient-primary;
        border-radius: $border-radius;
    }
}

// Modern list groups
.list-group {
    border-radius: $border-radius;
    box-shadow: $box-shadow;

    .list-group-item {
        border: none;
        border-bottom: 1px solid #f1f5f9;
        padding: 1rem 1.5rem;
        transition: all 0.3s ease;

        &:first-child {
            border-top-left-radius: $border-radius;
            border-top-right-radius: $border-radius;
        }

        &:last-child {
            border-bottom-left-radius: $border-radius;
            border-bottom-right-radius: $border-radius;
            border-bottom: none;
        }

        &:hover {
            background: rgba($primary, 0.02);
            transform: translateX(5px);
        }

        &.active {
            background: $gradient-primary;
            border-left: 4px solid darken($primary, 10%);
        }
    }
}

// Dark mode support
[data-bs-theme="dark"] {
    body {
        background: $body-bg-dark;
        color: #e2e8f0;
    }

    .navbar {
        background: rgba(15, 23, 42, 0.95) !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .navbar-brand, .nav-link {
            color: #e2e8f0 !important;
        }
    }

    .card {
        background: #1e293b;
        color: #e2e8f0;

        .card-header {
            background: linear-gradient(135deg, rgba($primary, 0.1) 0%, rgba($secondary, 0.1) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
    }

    .form-control, .form-select {
        background: #1e293b;
        border-color: #334155;
        color: #e2e8f0;

        &:focus {
            background: #1e293b;
            border-color: $primary;
            color: #e2e8f0;
        }
    }

    .table {
        color: #e2e8f0;

        thead th {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #94a3b8;
        }

        tbody td {
            border-bottom-color: #334155;
        }

        tbody tr:hover {
            background: rgba($primary, 0.1);
        }
    }

    .dropdown-menu {
        background: #1e293b;
        border: 1px solid #334155;

        .dropdown-item {
            color: #e2e8f0;

            &:hover {
                background: rgba($primary, 0.2);
            }
        }
    }

    .modal-content {
        background: #1e293b;
        color: #e2e8f0;

        .modal-header, .modal-footer {
            border-color: #334155;
        }
    }

    .list-group-item {
        background: #1e293b;
        color: #e2e8f0;
        border-bottom-color: #334155;

        &:hover {
            background: rgba($primary, 0.1);
        }
    }
}

// Mobile optimizations
@media (max-width: 768px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .card {
        margin-bottom: 1rem;

        .card-body {
            padding: 1rem;
        }
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }

    .table-responsive {
        border-radius: $border-radius;
        box-shadow: $box-shadow;
    }

    .navbar {
        padding: 0.5rem 1rem;

        .navbar-brand {
            font-size: 1.1rem;
        }
    }

    .modal-dialog {
        margin: 1rem;

        .modal-content {
            border-radius: $border-radius;
        }
    }
}

// Tablet optimizations
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        max-width: 100%;
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .card-columns {
        column-count: 2;
    }
}

// Desktop optimizations
@media (min-width: 1025px) {
    .hover-effects-enabled {
        .card:hover {
            transform: translateY(-5px);
            box-shadow: $box-shadow-xl;
        }

        .btn:hover {
            transform: translateY(-2px);
        }
    }
}

// Loading states
.loading {
    position: relative;
    pointer-events: none;

    &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid transparent;
        border-top: 2px solid $primary;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
