{% if settings.side_menu_dashboard %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-3 py-3 px-4 rounded-3 hover-lift" href="{{ '/'|link }}">
            <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                <i class="fas fa-tachometer-alt text-primary"></i>
            </div>
            <span class="fw-semibold">{{ 'Dashboard'|trans }}</span>
        </a>
    </li>
{% endif %}

{% if settings.side_menu_order %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-3 py-3 px-4 rounded-3 hover-lift" href="{{ '/order'|link }}">
            <div class="bg-success bg-opacity-10 rounded-circle p-2">
                <i class="fas fa-shopping-cart text-success"></i>
            </div>
            <span class="fw-semibold">{{ 'Order'|trans }}</span>
        </a>
    </li>
{% endif %}

{% if client or not settings.hide_menu %}

{% if settings.side_menu_support %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-3 py-3 px-4 rounded-3 hover-lift" href="{{ '/support'|link }}">
            <div class="bg-info bg-opacity-10 rounded-circle p-2">
                <i class="fas fa-headset text-info"></i>
            </div>
            <span class="fw-semibold">{{ 'Support'|trans }}</span>
        </a>
    </li>
{% endif %}

{% if settings.side_menu_services %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-3 py-3 px-4 rounded-3 hover-lift" href="{{ '/order/service'|link }}">
            <div class="bg-secondary bg-opacity-10 rounded-circle p-2">
                <i class="fas fa-server text-secondary"></i>
            </div>
            <span class="fw-semibold">{{ 'Services'|trans }}</span>
        </a>
    </li>
{% endif %}

{% if settings.side_menu_invoices %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-3 py-3 px-4 rounded-3 hover-lift" href="{{ '/invoice'|link }}">
            <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                <i class="fas fa-file-invoice-dollar text-warning"></i>
            </div>
            <span class="fw-semibold">{{ 'Invoices'|trans }}</span>
        </a>
    </li>
{% endif %}

{% if settings.side_menu_emails %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-2" href="{{ '/email'|link }}">
            <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M21.03 6.29L12 .64L2.97 6.29C2.39 6.64 2 7.27 2 8V18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V8C22 7.27 21.61 6.64 21.03 6.29M20 18H4V10L12 15L20 10V18M12 13L4 8L12 3L20 8L12 13Z" /></svg>
            {{ 'Email'|trans }}
        </a>
    </li>
{% endif %}

{% if settings.side_menu_payments %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-2" href="{{ '/client/balance'|link }}">
            <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15.5 15.5C16.33 15.5 17 14.83 17 14C17 13.17 16.33 12.5 15.5 12.5C14.67 12.5 14 13.17 14 14C14 14.83 14.67 15.5 15.5 15.5M7 3H17C18.11 3 19 3.9 19 5V7C20.11 7 21 7.9 21 9V19C21 20.11 20.11 21 19 21H7C4.79 21 3 19.21 3 17V7C3 4.79 4.79 3 7 3M17 7V5H7C5.9 5 5 5.9 5 7V7.54C5.59 7.2 6.27 7 7 7H17M5 17C5 18.11 5.9 19 7 19H19V9H7C5.9 9 5 9.9 5 11V17Z" /></svg>
            {{ 'Wallet'|trans }}
        </a>
    </li>
{% endif %}

{% if (guest.extension_is_on({"mod":"news"}) and settings.side_menu_news) %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-2" href="{{ '/news'|link }}">
            <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 5L20 19L4 19L4 5H20M20 3H4C2.89 3 2 3.89 2 5V19C2 20.11 2.89 21 4 21H20C21.11 21 22 20.11 22 19V5C22 3.89 21.11 3 20 3M18 15H6V17H18V15M10 7H6V13H10V7M12 9H18V7H12V9M18 11H12V13H18V11Z" /></svg>
            {{ 'News'|trans }}
        </a>
    </li>
{% endif %}

{% if (guest.support_kb_enabled() and settings.side_menu_kb) %}
    <li class="nav-item">
        <a class="nav-link d-flex align-items-center gap-2" href="{{ 'support/kb'|link }}">
            <svg class="svg-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 1L14 6V17L19 12.5V1M21 5V18.5C19.9 18.15 18.7 18 17.5 18C15.8 18 13.35 18.65 12 19.5V6C10.55 4.9 8.45 4.5 6.5 4.5C4.55 4.5 2.45 4.9 1 6V20.65C1 20.9 1.25 21.15 1.5 21.15C1.6 21.15 1.65 21.1 1.75 21.1C3.1 20.45 5.05 20 6.5 20C8.45 20 10.55 20.4 12 21.5C13.35 20.65 15.8 20 17.5 20C19.15 20 20.85 20.3 22.25 21.05C22.35 21.1 22.4 21.1 22.5 21.1C22.75 21.1 23 20.85 23 20.6V6C22.4 5.55 21.75 5.25 21 5M10 18.41C8.75 18.09 7.5 18 6.5 18C5.44 18 4.18 18.19 3 18.5V7.13C3.91 6.73 5.14 6.5 6.5 6.5C7.86 6.5 9.09 6.73 10 7.13V18.41Z" /></svg>
            {{ 'Knowledge Base'|trans }}
        </a>
    </li>
{% endif %}

{% if settings.sidebar_balance_enabled and client %}
    <li class="pt-3 ps-3">
        <h5 class="text-secondary d-block pb-1">{{ 'Account balance'|trans }}</h5>
        <h4><strong>{{ profile.balance | money(profile.currency) }}</strong></h4>
    </li>
{% endif %}

{% if settings.sidebar_note_enabled %}
    <li class="pt-3 ps-3">
        <h5 class="text-secondary d-block pb-1">{{ settings.sidebar_note_title }}</h5>
        <span>{{ settings.sidebar_note_content }}</span>
    </li>
{% endif %}

{% endif %}

{% if settings.top_menu_signout %}
    <li class="nav-item">
        {% if client %}
            <div class="dropdown">
                <button class="btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    <img class="img-fluid rounded-circle" alt="{{ profile.first_name }} {{ profile.last_name }} gravatar" src="{{ profile.email|gravatar(60) }}" height="25px" width="25px">
                    {% if profile.company %}
                        <span>{{ profile.first_name }} {{ profile.last_name }} ({{ profile.company }})</span>
                    {% else %}
                        <span>{{ profile.first_name }} {{ profile.last_name }}</span>
                    {% endif %}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item"
                           href="{{ 'client/profile'|link }}">{{ 'Profile'|trans }}</a></li>
                    <li><a class="dropdown-item"
                           href="{{ 'client/logout'|link }}">{{ 'Sign out'|trans }}</a></li>
                </ul>
            </div>
        {% else %}
            <div class="row pt-2">
                <div class="col">
                    <a class="btn btn-outline-primary mb-2 w-100"
                       href="{{ 'login'|link }}">{{ 'Login'|trans }}</a>
                </div>
                <div class="col">
                    <a class="btn btn-outline-primary mb-2 w-100"
                       href="{{ 'signup'|link }}">{{ 'Register'|trans }}</a>
                </div>
            </div>
        {% endif %}
    </li>
{% endif %}
