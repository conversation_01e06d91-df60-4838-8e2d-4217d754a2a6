{"name": "widdx", "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"bootstrap": "^5.3.7", "core-js": "^3.44.0", "flag-icons": "^7.5.0", "github-markdown-css": "^5.7.0", "jquery": "^3.7.1", "tom-select": "^2.4.3"}, "devDependencies": {"@symfony/webpack-encore": "^5.1.0", "autoprefixer": "^10.4.21", "postcss-loader": "^8.1.1", "sass": "^1.89.2", "sass-loader": "^16.0.5"}}